package com.example.project.adapters;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.example.project.R;
import com.example.project.models.FavoriteItem;
import com.example.project.models.Restaurant;
import com.google.android.material.imageview.ShapeableImageView;
import java.util.ArrayList;
import java.util.List;

/**
 * RecyclerView adapter for displaying user's favorite restaurants
 */
public class FavoritesAdapter extends RecyclerView.Adapter<FavoritesAdapter.FavoriteViewHolder> {
    
    private List<FavoriteItem> favorites = new ArrayList<>();
    private OnFavoriteClickListener clickListener;
    
    // Array of random restaurant images to use
    private static final int[] RESTAURANT_IMAGES = {
        R.drawable.restaurant_bella_trattoria,
        R.drawable.restaurant_sakura_sushi,
        R.drawable.restaurant_el_taco_loco,
        R.drawable.restaurant_le_petit_bistro,
        R.drawable.restaurant_spice_route,
        R.drawable.restaurant_italian,
        R.drawable.restaurant_burger
    };
    
    public interface OnFavoriteClickListener {
        void onFavoriteClick(Restaurant restaurant);
    }
    
    public FavoritesAdapter(OnFavoriteClickListener clickListener) {
        this.clickListener = clickListener;
    }
    
    @NonNull
    @Override
    public FavoriteViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_favorite_restaurant, parent, false);
        return new FavoriteViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull FavoriteViewHolder holder, int position) {
        FavoriteItem favoriteItem = favorites.get(position);
        Restaurant restaurant = favoriteItem.getRestaurantData();
        
        if (restaurant != null) {
            holder.bind(restaurant, position);
        }
    }
    
    @Override
    public int getItemCount() {
        return favorites.size();
    }
    
    public void updateFavorites(List<FavoriteItem> newFavorites) {
        this.favorites.clear();
        if (newFavorites != null) {
            this.favorites.addAll(newFavorites);
        }
        notifyDataSetChanged();
    }
    
    class FavoriteViewHolder extends RecyclerView.ViewHolder {
        private final ShapeableImageView restaurantImage;
        private final TextView restaurantName;
        private final TextView restaurantDescription;
        private final TextView restaurantDetails;
        
        public FavoriteViewHolder(@NonNull View itemView) {
            super(itemView);
            restaurantImage = itemView.findViewById(R.id.restaurant_image);
            restaurantName = itemView.findViewById(R.id.restaurant_name);
            restaurantDescription = itemView.findViewById(R.id.restaurant_description);
            restaurantDetails = itemView.findViewById(R.id.restaurant_details);
        }
        
        public void bind(Restaurant restaurant, int position) {
            restaurantName.setText(restaurant.getName());
            restaurantDescription.setText(restaurant.getDescription());
            restaurantDetails.setText(restaurant.getCuisineType() + " · " + String.format("%.1f", restaurant.getRating()));
            
            // Set a random restaurant image
            int imageIndex = position % RESTAURANT_IMAGES.length;
            restaurantImage.setImageResource(RESTAURANT_IMAGES[imageIndex]);
            
            // Set click listener
            itemView.setOnClickListener(v -> {
                if (clickListener != null) {
                    clickListener.onFavoriteClick(restaurant);
                }
            });
        }
    }
}
