<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/favorites_main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_primary"
    tools:context=".FavoritesActivity">

    <!-- Main Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="80dp"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Header Section -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingHorizontal="16dp"
                android:paddingTop="16dp"
                android:paddingBottom="8dp">

                <!-- Back Arrow -->
                <ImageView
                    android:id="@+id/back_arrow"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:src="@drawable/ic_back_arrow"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:padding="12dp"
                    android:contentDescription="Back"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:tint="@color/text_primary" />

                <!-- Favorites Title -->
                <TextView
                    android:id="@+id/favorites_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="Favorites"
                    android:textAppearance="@style/TextAppearance.Foodie.AppTitle"
                    app:layout_constraintStart_toEndOf="@+id/back_arrow"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- Favorites Restaurant List -->
            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp">

                <!-- RecyclerView for favorites -->
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/favorites_recycler_view"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone" />

                <!-- Empty state -->
                <TextView
                    android:id="@+id/empty_state_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="No favorites yet. Start exploring restaurants!"
                    android:textAppearance="@style/TextAppearance.Foodie.FavoriteDescription"
                    android:textAlignment="center"
                    android:padding="32dp"
                    android:visibility="visible" />

            </FrameLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Bottom Navigation -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:orientation="horizontal"
        android:background="@color/background_primary"
        android:elevation="8dp"
        android:paddingVertical="8dp"
        android:paddingHorizontal="16dp"
        android:paddingBottom="12dp">

        <!-- Home Tab (Inactive) -->
        <LinearLayout
            android:id="@+id/home_tab"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:padding="8dp"
            android:layout_marginEnd="4dp"
            android:clickable="true"
            android:focusable="true"
            android:background="?attr/selectableItemBackground">

            <ImageView
                android:layout_width="18dp"
                android:layout_height="19dp"
                android:src="@drawable/ic_home"
                app:tint="@color/text_secondary_favorites" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Home"
                android:textAppearance="@style/TextAppearance.Foodie.BottomNav"
                android:textColor="@color/text_secondary_favorites"
                android:layout_marginTop="4dp" />

        </LinearLayout>

        <!-- Favorites Tab (Active) -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:background="@color/background_secondary"
            android:padding="8dp"
            android:layout_marginHorizontal="4dp"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:layout_width="21dp"
                android:layout_height="18dp"
                android:src="@drawable/ic_favorites"
                app:tint="@color/text_primary" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Favorites"
                android:textAppearance="@style/TextAppearance.Foodie.BottomNav"
                android:textColor="@color/text_primary"
                android:layout_marginTop="4dp" />

        </LinearLayout>

        <!-- Profile Tab (Inactive) -->
        <LinearLayout
            android:id="@+id/profile_tab"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:padding="8dp"
            android:layout_marginStart="4dp"
            android:clickable="true"
            android:focusable="true"
            android:background="?attr/selectableItemBackground">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="19dp"
                android:src="@drawable/ic_profile"
                app:tint="@color/text_secondary_favorites" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Profile"
                android:textAppearance="@style/TextAppearance.Foodie.BottomNav"
                android:textColor="@color/text_secondary_favorites"
                android:layout_marginTop="4dp" />

        </LinearLayout>

    </LinearLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
