<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="8dp"
    android:gravity="center_vertical">

    <!-- Restaurant Image -->
    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/restaurant_image"
        android:layout_width="56dp"
        android:layout_height="56dp"
        android:layout_marginEnd="16dp"
        android:scaleType="centerCrop"
        android:src="@drawable/restaurant_italian"
        app:shapeAppearanceOverlay="@style/ShapeAppearanceOverlay.Material3.Corner.Medium" />

    <!-- Restaurant Info -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/restaurant_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="The Italian Place"
            android:textAppearance="@style/TextAppearance.Foodie.RestaurantName" />

    </LinearLayout>

    <!-- Rating -->
    <TextView
        android:id="@+id/restaurant_rating"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="4.5 ★"
        android:textAppearance="@style/TextAppearance.Foodie.Rating" />

</LinearLayout>
