<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    android:layout_marginBottom="4dp">

    <!-- Rating Number -->
    <TextView
        android:id="@+id/rating_number"
        android:layout_width="16dp"
        android:layout_height="wrap_content"
        android:text="5"
        android:textAppearance="@style/TextAppearance.Foodie.RatingNumber"
        android:layout_marginEnd="8dp" />

    <!-- Progress Bar Background -->
    <FrameLayout
        android:layout_width="0dp"
        android:layout_height="8dp"
        android:layout_weight="1"
        android:layout_marginEnd="8dp"
        android:background="@drawable/progress_bar_background">

        <!-- Progress Bar Fill -->
        <View
            android:id="@+id/progress_fill"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/progress_bar_fill" />

    </FrameLayout>

    <!-- Percentage -->
    <TextView
        android:id="@+id/rating_percentage"
        android:layout_width="32dp"
        android:layout_height="wrap_content"
        android:text="40%"
        android:textAppearance="@style/TextAppearance.Foodie.RatingPercentage"
        android:gravity="end" />

</LinearLayout>
