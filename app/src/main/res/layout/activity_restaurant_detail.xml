<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/restaurant_detail_main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_primary"
    tools:context=".RestaurantDetailActivity">

    <!-- Main Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Restaurant Image Section with Header Overlay -->
            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="200dp">

                <!-- Restaurant Hero Image -->
                <ImageView
                    android:id="@+id/restaurant_hero_image"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="centerCrop"
                    android:src="@drawable/restaurant_golden_spoon" />

                <!-- Header Overlay -->
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingHorizontal="16dp"
                    android:paddingTop="16dp"
                    android:paddingBottom="8dp">

                    <!-- Back Arrow -->
                    <ImageView
                        android:id="@+id/back_arrow"
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:src="@drawable/ic_back_arrow_detail"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:padding="12dp"
                        android:contentDescription="Back"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:tint="@color/text_primary" />

                    <!-- Share Button -->
                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/share_button"
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        app:cardBackgroundColor="@color/background_primary"
                        app:cardCornerRadius="24dp"
                        app:cardElevation="2dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <ImageView
                            android:layout_width="20dp"
                            android:layout_height="17dp"
                            android:layout_gravity="center"
                            android:src="@drawable/ic_share"
                            android:contentDescription="Share"
                            app:tint="@color/text_primary" />

                    </com.google.android.material.card.MaterialCardView>

                </androidx.constraintlayout.widget.ConstraintLayout>

            </FrameLayout>

            <!-- Restaurant Info Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingHorizontal="16dp"
                android:paddingTop="20dp"
                android:paddingBottom="12dp">

                <!-- Restaurant Name -->
                <TextView
                    android:id="@+id/restaurant_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="The Golden Spoon"
                    android:textAppearance="@style/TextAppearance.Foodie.RestaurantDetailName" />

            </LinearLayout>

            <!-- Restaurant Description Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingHorizontal="16dp"
                android:paddingVertical="4dp">

                <!-- Description -->
                <TextView
                    android:id="@+id/restaurant_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="The Golden Spoon offers a delightful Italian dining experience with a menu featuring classic pasta dishes, wood-fired pizzas, and fresh seafood. Enjoy a cozy atmosphere and attentive service."
                    android:textAppearance="@style/TextAppearance.Foodie.RestaurantDescription"
                    android:layout_marginBottom="12dp" />

            </LinearLayout>




            <!-- Restaurant Details Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingHorizontal="16dp"
                android:paddingVertical="4dp">

                <!-- Rating Section -->
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="8dp">

                    <TextView
                        android:id="@+id/restaurant_rating"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="4.5"
                        android:textAppearance="@style/TextAppearance.Foodie.RestaurantDetails"
                        android:textStyle="bold"
                        android:layout_marginEnd="4dp" />

                    <ImageView
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:src="@drawable/ic_star_filled"
                        android:layout_marginEnd="8dp"
                        app:tint="@color/star_yellow" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Rating"
                        android:textAppearance="@style/TextAppearance.Foodie.RestaurantDetails"
                        android:textColor="@color/text_secondary" />

                </LinearLayout>

                <!-- Address and Info -->
                <TextView
                    android:id="@+id/restaurant_info"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="123 Main Street, Anytown | Italian | $$"
                    android:textAppearance="@style/TextAppearance.Foodie.RestaurantDetails"
                    android:layout_marginBottom="8dp" />

                <!-- Phone Number -->
                <TextView
                    android:id="@+id/restaurant_phone"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Phone: (*************"
                    android:textAppearance="@style/TextAppearance.Foodie.RestaurantDetails"
                    android:layout_marginBottom="8dp"
                    android:autoLink="phone" />

                <!-- Hours -->
                <TextView
                    android:id="@+id/restaurant_hours"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Open today: 11:00 AM - 10:00 PM"
                    android:textAppearance="@style/TextAppearance.Foodie.RestaurantDetails" />

            </LinearLayout>

            <!-- Action Buttons Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingHorizontal="16dp"
                android:paddingVertical="12dp">

                <!-- Directions Button -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/directions_button"
                    android:layout_width="0dp"
                    android:layout_height="40dp"
                    android:layout_weight="1"
                    android:layout_marginEnd="12dp"
                    android:text="Directions"
                    android:textAppearance="@style/TextAppearance.Foodie.ActionButton"
                    android:textColor="@color/text_primary"
                    app:backgroundTint="@color/button_gray"
                    app:cornerRadius="20dp"
                    style="@style/Widget.Material3.Button.UnelevatedButton" />

                <!-- Favorite Button -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/favorite_button"
                    android:layout_width="0dp"
                    android:layout_height="40dp"
                    android:layout_weight="1"
                    android:text="Add to Favorite"
                    android:textAppearance="@style/TextAppearance.Foodie.ActionButton"
                    android:textColor="@color/background_primary"
                    app:backgroundTint="@color/fab_red"
                    app:cornerRadius="20dp"
                    style="@style/Widget.Material3.Button.UnelevatedButton" />

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
