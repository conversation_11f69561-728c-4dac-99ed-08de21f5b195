<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/search_main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_primary"
    tools:context=".SearchActivity">

    <!-- Main Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="20dp"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Header Section -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingHorizontal="16dp"
                android:paddingTop="16dp"
                android:paddingBottom="8dp">

                <!-- Back Arrow -->
                <ImageView
                    android:id="@+id/back_arrow"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:src="@drawable/ic_back_arrow_search"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:padding="12dp"
                    android:contentDescription="Back"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:tint="@color/text_primary" />

                <!-- Search Title -->
                <TextView
                    android:id="@+id/search_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="Search"
                    android:textAppearance="@style/TextAppearance.Foodie.AppTitle"
                    app:layout_constraintStart_toEndOf="@+id/back_arrow"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- Search Bar Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingHorizontal="16dp"
                android:paddingVertical="12dp">

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    style="@style/Widget.Foodie.SearchCard">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingStart="16dp"
                        android:paddingEnd="8dp">

                        <ImageView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:src="@drawable/ic_search_active"
                            android:layout_marginEnd="8dp"
                            app:tint="@color/text_secondary" />

                        <EditText
                            android:id="@+id/search_edit_text"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Italian"
                            android:textAppearance="@style/TextAppearance.Foodie.SearchText"
                            android:background="@android:color/transparent"
                            android:hint="Search restaurants or cuisines"
                            android:textColorHint="@color/text_secondary"
                            android:imeOptions="actionSearch"
                            android:inputType="text"
                            android:maxLines="1" />

                        <ImageView
                            android:id="@+id/clear_search"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_clear"
                            android:background="?attr/selectableItemBackgroundBorderless"
                            android:padding="2dp"
                            android:contentDescription="Clear search"
                            app:tint="@color/text_secondary" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

            </LinearLayout>


            <!-- Search Results Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- Loading State -->
                <LinearLayout
                    android:id="@+id/loading_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:layout_marginTop="32dp"
                    android:layout_marginBottom="32dp"
                    android:visibility="gone">

                    <ProgressBar
                        android:id="@+id/loading_progress"
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:layout_marginBottom="16dp"
                        android:indeterminateTint="@color/fab_red" />

                    <TextView
                        android:id="@+id/loading_message"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Please wait while we search for restaurants..."
                        android:textAppearance="@style/TextAppearance.Foodie.SearchReviews"
                        android:textColor="@color/text_secondary"
                        android:gravity="center" />

                </LinearLayout>

                <!-- Search Results Header -->
                <TextView
                    android:id="@+id/search_results_header"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Search Results"
                    android:textAppearance="@style/TextAppearance.Foodie.RestaurantName"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="8dp"
                    android:layout_marginBottom="8dp"
                    android:visibility="gone" />

                <!-- RecyclerView for Dynamic Search Results -->
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/search_results_recycler"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:nestedScrollingEnabled="false"
                    android:visibility="gone" />

                <!-- No Results Message -->
                <TextView
                    android:id="@+id/no_results_message"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="No restaurants found. Try a different search term."
                    android:textAppearance="@style/TextAppearance.Foodie.SearchReviews"
                    android:layout_gravity="center"
                    android:layout_marginTop="32dp"
                    android:layout_marginBottom="32dp"
                    android:visibility="gone" />

                <!-- Default Message (shown when no search performed) -->
                <TextView
                    android:id="@+id/default_message"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Type in the search bar above to find restaurants"
                    android:textAppearance="@style/TextAppearance.Foodie.SearchReviews"
                    android:layout_gravity="center"
                    android:layout_marginTop="32dp"
                    android:layout_marginBottom="32dp"
                    android:visibility="visible" />
            </LinearLayout>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.coordinatorlayout.widget.CoordinatorLayout>
