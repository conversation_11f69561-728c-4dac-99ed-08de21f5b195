<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="12dp"
    android:paddingHorizontal="16dp"
    android:gravity="center_vertical">

    <!-- Restaurant Image -->
    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/favorite_restaurant_image"
        android:layout_width="70dp"
        android:layout_height="70dp"
        android:layout_marginEnd="16dp"
        android:scaleType="centerCrop"
        android:src="@drawable/restaurant_bella_trattoria"
        app:shapeAppearanceOverlay="@style/ShapeAppearanceOverlay.Material3.Corner.Medium" />

    <!-- Restaurant Info -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <!-- Restaurant Name -->
        <TextView
            android:id="@+id/favorite_restaurant_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Bella Trattoria"
            android:textAppearance="@style/TextAppearance.Foodie.RestaurantName"
            android:layout_marginBottom="2dp" />

        <!-- Restaurant Description -->
        <TextView
            android:id="@+id/favorite_restaurant_description"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Authentic Italian cuisine with a modern twist."
            android:textAppearance="@style/TextAppearance.Foodie.FavoriteDescription"
            android:layout_marginBottom="2dp" />

        <!-- Cuisine and Rating -->
        <TextView
            android:id="@+id/favorite_restaurant_details"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Italian · 4.5"
            android:textAppearance="@style/TextAppearance.Foodie.FavoriteDetails" />

    </LinearLayout>

</LinearLayout>
