<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_primary"
    tools:context=".MainActivity">

    <!-- Main Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="80dp"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Header Section -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingHorizontal="16dp"
                android:paddingTop="16dp"
                android:paddingBottom="8dp">

                <TextView
                    android:id="@+id/app_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="Foodie"
                    android:textAppearance="@style/TextAppearance.Foodie.AppTitle"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/menu_icon"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />

                <ImageView
                    android:id="@+id/menu_icon"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:src="@drawable/ic_menu"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:padding="12dp"
                    android:contentDescription="Menu"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:tint="@color/text_primary" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- Search Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingHorizontal="16dp"
                android:paddingVertical="12dp">

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/search_card"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    style="@style/Widget.Foodie.SearchCard">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingStart="16dp"
                        android:paddingEnd="8dp">

                        <ImageView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:src="@drawable/ic_search"
                            android:layout_marginEnd="8dp"
                            app:tint="@color/text_secondary" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Search restaurants or cuisines"
                            android:textAppearance="@style/TextAppearance.Foodie.SearchHint"
                            android:background="@android:color/transparent" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

            </LinearLayout>

            <!-- Trending Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingHorizontal="16dp"
                android:paddingTop="20dp"
                android:paddingBottom="12dp">

                <TextView
                    android:id="@+id/favorites_section_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Trending"
                    android:textAppearance="@style/TextAppearance.Foodie.SectionTitle"
                    android:layout_marginBottom="12dp" />

                <!-- Trending List -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <!-- The Italian Place -->
                    <include
                        layout="@layout/restaurant_card_item"
                        android:id="@+id/italian_restaurant" />

                    <!-- Sushi Central -->
                    <LinearLayout
                        android:id="@+id/sushi_restaurant"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="8dp"
                        android:gravity="center_vertical"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true">

                        <com.google.android.material.imageview.ShapeableImageView
                            android:layout_width="56dp"
                            android:layout_height="56dp"
                            android:layout_marginEnd="16dp"
                            android:scaleType="centerCrop"
                            android:src="@drawable/restaurant_sushi"
                            app:shapeAppearanceOverlay="@style/ShapeAppearanceOverlay.Material3.Corner.Medium" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Sushi Central"
                                android:textAppearance="@style/TextAppearance.Foodie.RestaurantName" />

                        </LinearLayout>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="4.2 ★"
                            android:textAppearance="@style/TextAppearance.Foodie.Rating" />

                    </LinearLayout>

                    <!-- Burger Haven -->
                    <LinearLayout
                        android:id="@+id/burger_restaurant"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="8dp"
                        android:gravity="center_vertical"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true">

                        <com.google.android.material.imageview.ShapeableImageView
                            android:layout_width="56dp"
                            android:layout_height="56dp"
                            android:layout_marginEnd="16dp"
                            android:scaleType="centerCrop"
                            android:src="@drawable/restaurant_burger"
                            app:shapeAppearanceOverlay="@style/ShapeAppearanceOverlay.Material3.Corner.Medium" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Burger Haven"
                                android:textAppearance="@style/TextAppearance.Foodie.RestaurantName" />

                        </LinearLayout>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="4.8 ★"
                            android:textAppearance="@style/TextAppearance.Foodie.Rating" />

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

            <!-- Discover New Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingHorizontal="16dp"
                android:paddingTop="20dp"
                android:paddingBottom="12dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Discover New"
                    android:textAppearance="@style/TextAppearance.Foodie.SectionTitle"
                    android:layout_marginBottom="12dp" />

                <!-- Horizontal Scrolling Discover Cards -->
                <HorizontalScrollView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:scrollbars="none">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:paddingEnd="16dp">

                        <!-- Taco Fiesta Card -->
                        <include
                            layout="@layout/discover_card_item"
                            android:id="@+id/taco_restaurant" />

                        <!-- Noodle Nirvana Card -->
                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/noodle_restaurant"
                            android:layout_width="280dp"
                            android:layout_height="wrap_content"
                            style="@style/Widget.Foodie.DiscoverCard"
                            android:clickable="true"
                            android:focusable="true">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal">

                                <LinearLayout
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:orientation="vertical"
                                    android:padding="16dp">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="New"
                                        android:textAppearance="@style/TextAppearance.Foodie.NewBadge"
                                        android:layout_marginBottom="4dp" />

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="Noodle Nirvana"
                                        android:textAppearance="@style/TextAppearance.Foodie.RestaurantName"
                                        android:textStyle="bold"
                                        android:layout_marginBottom="4dp" />

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="Asian Fusion · 4.3 ★"
                                        android:textAppearance="@style/TextAppearance.Foodie.Rating" />

                                </LinearLayout>

                                <com.google.android.material.imageview.ShapeableImageView
                                    android:layout_width="70dp"
                                    android:layout_height="70dp"
                                    android:layout_gravity="center_vertical"
                                    android:layout_marginEnd="16dp"
                                    android:scaleType="centerCrop"
                                    android:src="@drawable/restaurant_noodle"
                                    app:shapeAppearanceOverlay="@style/ShapeAppearanceOverlay.Material3.Corner.Medium" />

                            </LinearLayout>

                        </com.google.android.material.card.MaterialCardView>

                    </LinearLayout>

                </HorizontalScrollView>

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Bottom Navigation -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:orientation="horizontal"
        android:background="@color/background_primary"
        android:elevation="8dp"
        android:paddingVertical="8dp"
        android:paddingHorizontal="16dp"
        android:paddingBottom="12dp">

        <!-- Home Tab (Active) -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:background="@color/colorSecondary"
            android:padding="8dp"
            android:layout_marginEnd="4dp"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:layout_width="18dp"
                android:layout_height="19dp"
                android:src="@drawable/ic_home"
                app:tint="@color/text_primary" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Home"
                android:textAppearance="@style/TextAppearance.Foodie.BottomNav"
                android:textColor="@color/text_primary"
                android:layout_marginTop="4dp" />

        </LinearLayout>

        <!-- Favorites Tab -->
        <LinearLayout
            android:id="@+id/favorites_tab"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:padding="8dp"
            android:layout_marginHorizontal="4dp"
            android:clickable="true"
            android:focusable="true"
            android:background="?attr/selectableItemBackground">

            <ImageView
                android:layout_width="21dp"
                android:layout_height="18dp"
                android:src="@drawable/ic_favorites"
                app:tint="@color/text_secondary" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Favorites"
                android:textAppearance="@style/TextAppearance.Foodie.BottomNav"
                android:textColor="@color/text_secondary"
                android:layout_marginTop="4dp" />

        </LinearLayout>

        <!-- Profile Tab -->
        <LinearLayout
            android:id="@+id/profile_tab"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:padding="8dp"
            android:layout_marginStart="4dp"
            android:clickable="true"
            android:focusable="true"
            android:background="?attr/selectableItemBackground">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="19dp"
                android:src="@drawable/ic_profile"
                app:tint="@color/text_secondary" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Profile"
                android:textAppearance="@style/TextAppearance.Foodie.BottomNav"
                android:textColor="@color/text_secondary"
                android:layout_marginTop="4dp" />

        </LinearLayout>

    </LinearLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>