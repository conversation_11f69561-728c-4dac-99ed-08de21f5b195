<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="8dp"
    android:paddingHorizontal="16dp"
    android:gravity="center_vertical">

    <!-- Icon Background -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginEnd="16dp"
        app:cardBackgroundColor="@color/icon_background"
        app:cardCornerRadius="8dp"
        app:cardElevation="0dp">

        <ImageView
            android:id="@+id/preference_icon"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_gravity="center"
            android:src="@drawable/ic_dietary_restrictions"
            app:tint="@color/text_primary" />

    </com.google.android.material.card.MaterialCardView>

    <!-- Preference Info -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <!-- Preference Title -->
        <TextView
            android:id="@+id/preference_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Dietary Restrictions"
            android:textAppearance="@style/TextAppearance.Foodie.ProfileItemTitle"
            android:layout_marginBottom="2dp" />

        <!-- Preference Detail -->
        <TextView
            android:id="@+id/preference_detail"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Vegetarian, Gluten-Free"
            android:textAppearance="@style/TextAppearance.Foodie.ProfileItemDetail" />

    </LinearLayout>

</LinearLayout>
