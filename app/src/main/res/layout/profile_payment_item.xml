<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:paddingHorizontal="16dp"
    android:paddingVertical="8dp"
    android:gravity="center_vertical">

    <!-- Payment Card Icon -->
    <ImageView
        android:id="@+id/payment_icon"
        android:layout_width="40dp"
        android:layout_height="24dp"
        android:layout_marginEnd="16dp"
        android:src="@drawable/ic_mastercard"
        android:scaleType="fitCenter" />

    <!-- Payment Info -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <!-- Payment Method -->
        <TextView
            android:id="@+id/payment_method"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Mastercard ...4242"
            android:textAppearance="@style/TextAppearance.Foodie.ProfileItemTitle" />

    </LinearLayout>

</LinearLayout>
