<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="12dp"
    android:paddingHorizontal="16dp"
    android:gravity="center_vertical"
    android:background="?attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/restaurant_image"
        android:layout_width="70dp"
        android:layout_height="70dp"
        android:layout_marginEnd="16dp"
        android:scaleType="centerCrop"
        android:src="@drawable/restaurant_bella_trattoria"
        app:shapeAppearanceOverlay="@style/ShapeAppearanceOverlay.Material3.Corner.Medium" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/restaurant_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Restaurant Name"
            android:textAppearance="@style/TextAppearance.Foodie.RestaurantName"
            android:layout_marginBottom="2dp" />

        <TextView
            android:id="@+id/restaurant_description"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Restaurant description goes here."
            android:textAppearance="@style/TextAppearance.Foodie.FavoriteDescription"
            android:layout_marginBottom="2dp"
            android:maxLines="2"
            android:ellipsize="end" />

        <TextView
            android:id="@+id/restaurant_details"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Cuisine · Rating"
            android:textAppearance="@style/TextAppearance.Foodie.FavoriteDetails" />

    </LinearLayout>

</LinearLayout>
