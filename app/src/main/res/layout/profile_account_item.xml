<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:paddingHorizontal="16dp"
    android:paddingVertical="8dp"
    android:gravity="center_vertical"
    android:background="?attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true">

    <!-- Icon Background -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginEnd="16dp"
        app:cardBackgroundColor="@color/icon_background"
        app:cardCornerRadius="8dp"
        app:cardElevation="0dp">

        <ImageView
            android:id="@+id/account_icon"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_gravity="center"
            android:src="@drawable/ic_edit_profile"
            app:tint="@color/text_primary" />

    </com.google.android.material.card.MaterialCardView>

    <!-- Account Option -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <!-- Account Option Title -->
        <TextView
            android:id="@+id/account_option"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Edit Profile"
            android:textAppearance="@style/TextAppearance.Foodie.ProfileItemTitle" />

    </LinearLayout>

</LinearLayout>
