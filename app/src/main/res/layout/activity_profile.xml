<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/profile_main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_primary"
    tools:context=".ProfileActivity">

    <!-- Main Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="80dp"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Header Section -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingHorizontal="16dp"
                android:paddingTop="16dp"
                android:paddingBottom="8dp">

                <!-- Back Arrow -->
                <ImageView
                    android:id="@+id/back_arrow"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:src="@drawable/ic_back_arrow_profile"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:padding="12dp"
                    android:contentDescription="Back"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:tint="@color/text_primary" />

                <!-- Profile Title -->
                <TextView
                    android:id="@+id/profile_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="Profile"
                    android:textAppearance="@style/TextAppearance.Foodie.AppTitle"
                    app:layout_constraintStart_toEndOf="@+id/back_arrow"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- User Profile Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:padding="16dp">

                <!-- Profile Avatar -->
                <com.google.android.material.imageview.ShapeableImageView
                    android:id="@+id/profile_avatar"
                    android:layout_width="128dp"
                    android:layout_height="128dp"
                    android:layout_marginBottom="16dp"
                    android:scaleType="centerCrop"
                    android:src="@drawable/profile_avatar"
                    app:shapeAppearanceOverlay="@style/ShapeAppearanceOverlay.Material3.Corner.Full" />

                <!-- User Name -->
                <TextView
                    android:id="@+id/user_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Sophia Chen"
                    android:textAppearance="@style/TextAppearance.Foodie.ProfileName"
                    android:layout_marginBottom="4dp" />

                <!-- User Email -->
                <TextView
                    android:id="@+id/user_email"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="<EMAIL>"
                    android:textAppearance="@style/TextAppearance.Foodie.ProfileEmail" />

            </LinearLayout>

            <!-- Dining Preferences Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- Section Title -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Dining Preferences"
                    android:textAppearance="@style/TextAppearance.Foodie.ProfileSectionTitle"
                    android:paddingHorizontal="16dp"
                    android:paddingTop="16dp"
                    android:paddingBottom="8dp" />

                <!-- Dietary Restrictions -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="8dp"
                    android:paddingHorizontal="16dp"
                    android:gravity="center_vertical">

                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:layout_marginEnd="16dp"
                        app:cardBackgroundColor="@color/icon_background"
                        app:cardCornerRadius="8dp"
                        app:cardElevation="0dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_gravity="center"
                            android:src="@drawable/ic_dietary_restrictions"
                            app:tint="@color/text_primary" />

                    </com.google.android.material.card.MaterialCardView>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Dietary Restrictions"
                            android:textAppearance="@style/TextAppearance.Foodie.ProfileItemTitle"
                            android:layout_marginBottom="2dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Vegetarian, Gluten-Free"
                            android:textAppearance="@style/TextAppearance.Foodie.ProfileItemDetail" />

                    </LinearLayout>

                </LinearLayout>

                <!-- Preferred Cuisine -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="8dp"
                    android:paddingHorizontal="16dp"
                    android:gravity="center_vertical">

                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:layout_marginEnd="16dp"
                        app:cardBackgroundColor="@color/icon_background"
                        app:cardCornerRadius="8dp"
                        app:cardElevation="0dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_gravity="center"
                            android:src="@drawable/ic_preferred_cuisine"
                            app:tint="@color/text_primary" />

                    </com.google.android.material.card.MaterialCardView>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Preferred Cuisine"
                            android:textAppearance="@style/TextAppearance.Foodie.ProfileItemTitle"
                            android:layout_marginBottom="2dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Casual Dining, Fine Dining"
                            android:textAppearance="@style/TextAppearance.Foodie.ProfileItemDetail" />

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

            <!-- Payment Methods Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- Section Title -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Payment Methods"
                    android:textAppearance="@style/TextAppearance.Foodie.ProfileSectionTitle"
                    android:paddingHorizontal="16dp"
                    android:paddingTop="16dp"
                    android:paddingBottom="8dp" />

                <!-- Mastercard -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:paddingHorizontal="16dp"
                    android:paddingVertical="8dp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="40dp"
                        android:layout_height="24dp"
                        android:layout_marginEnd="16dp"
                        android:src="@drawable/ic_mastercard"
                        android:scaleType="fitCenter" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Mastercard ...4242"
                        android:textAppearance="@style/TextAppearance.Foodie.ProfileItemTitle" />

                </LinearLayout>

                <!-- Visa -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:paddingHorizontal="16dp"
                    android:paddingVertical="8dp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="40dp"
                        android:layout_height="24dp"
                        android:layout_marginEnd="16dp"
                        android:src="@drawable/ic_visa"
                        android:scaleType="fitCenter" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Visa ...8765"
                        android:textAppearance="@style/TextAppearance.Foodie.ProfileItemTitle" />

                </LinearLayout>

            </LinearLayout>

            <!-- Account Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- Section Title -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Account"
                    android:textAppearance="@style/TextAppearance.Foodie.ProfileSectionTitle"
                    android:paddingHorizontal="16dp"
                    android:paddingTop="16dp"
                    android:paddingBottom="8dp" />

                <!-- Edit Profile -->
                <LinearLayout
                    android:id="@+id/edit_profile_option"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:paddingHorizontal="16dp"
                    android:paddingVertical="8dp"
                    android:gravity="center_vertical"
                    android:background="?attr/selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true">

                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_marginEnd="16dp"
                        app:cardBackgroundColor="@color/icon_background"
                        app:cardCornerRadius="8dp"
                        app:cardElevation="0dp">

                        <ImageView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_gravity="center"
                            android:src="@drawable/ic_edit_profile"
                            app:tint="@color/text_primary" />

                    </com.google.android.material.card.MaterialCardView>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Edit Profile"
                        android:textAppearance="@style/TextAppearance.Foodie.ProfileItemTitle" />

                </LinearLayout>

                <!-- Settings -->
                <LinearLayout
                    android:id="@+id/settings_option"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:paddingHorizontal="16dp"
                    android:paddingVertical="8dp"
                    android:gravity="center_vertical"
                    android:background="?attr/selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true">

                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_marginEnd="16dp"
                        app:cardBackgroundColor="@color/icon_background"
                        app:cardCornerRadius="8dp"
                        app:cardElevation="0dp">

                        <ImageView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_gravity="center"
                            android:src="@drawable/ic_settings"
                            app:tint="@color/text_primary" />

                    </com.google.android.material.card.MaterialCardView>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Settings"
                        android:textAppearance="@style/TextAppearance.Foodie.ProfileItemTitle" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Bottom Navigation -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:orientation="horizontal"
        android:background="@color/background_primary"
        android:elevation="8dp"
        android:paddingVertical="8dp"
        android:paddingHorizontal="16dp"
        android:paddingBottom="12dp">

        <!-- Home Tab (Inactive) -->
        <LinearLayout
            android:id="@+id/home_tab"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:padding="8dp"
            android:layout_marginEnd="4dp"
            android:clickable="true"
            android:focusable="true"
            android:background="?attr/selectableItemBackground">

            <ImageView
                android:layout_width="18dp"
                android:layout_height="19dp"
                android:src="@drawable/ic_home_inactive"
                app:tint="@color/text_secondary_favorites" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Home"
                android:textAppearance="@style/TextAppearance.Foodie.BottomNav"
                android:textColor="@color/text_secondary_favorites"
                android:layout_marginTop="4dp" />

        </LinearLayout>

        <!-- Favorites Tab (Inactive) -->
        <LinearLayout
            android:id="@+id/favorites_tab"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:padding="8dp"
            android:layout_marginHorizontal="4dp"
            android:clickable="true"
            android:focusable="true"
            android:background="?attr/selectableItemBackground">

            <ImageView
                android:layout_width="21dp"
                android:layout_height="18dp"
                android:src="@drawable/ic_favorites_inactive"
                app:tint="@color/text_secondary_favorites" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Favorites"
                android:textAppearance="@style/TextAppearance.Foodie.BottomNav"
                android:textColor="@color/text_secondary_favorites"
                android:layout_marginTop="4dp" />

        </LinearLayout>

        <!-- Profile Tab (Active) -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:background="@color/background_secondary"
            android:padding="8dp"
            android:layout_marginStart="4dp"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="19dp"
                android:src="@drawable/ic_profile_active"
                app:tint="@color/text_primary" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Profile"
                android:textAppearance="@style/TextAppearance.Foodie.BottomNav"
                android:textColor="@color/text_primary"
                android:layout_marginTop="4dp" />

        </LinearLayout>

    </LinearLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
