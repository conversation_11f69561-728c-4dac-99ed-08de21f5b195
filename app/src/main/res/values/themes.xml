<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Base.Theme.Project" parent="Theme.Material3.DayNight.NoActionBar">
        <!-- Customize your light theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryVariant">@color/colorPrimaryVariant</item>
        <item name="colorOnPrimary">@color/colorOnPrimary</item>
        <item name="colorSecondary">@color/colorSecondary</item>
        <item name="colorOnSecondary">@color/colorOnSecondary</item>
        <item name="colorSurface">@color/colorSurface</item>
        <item name="colorOnSurface">@color/colorOnSurface</item>
        <item name="android:windowBackground">@color/background_primary</item>
    </style>

    <style name="Theme.Project" parent="Base.Theme.Project" />

    <!-- Typography Styles -->
    <style name="TextAppearance.Foodie.AppTitle" parent="TextAppearance.Material3.HeadlineSmall">
        <item name="android:textSize">18sp</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textAlignment">center</item>
    </style>

    <style name="TextAppearance.Foodie.SectionTitle" parent="TextAppearance.Material3.HeadlineSmall">
        <item name="android:textSize">22sp</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="TextAppearance.Foodie.RestaurantName" parent="TextAppearance.Material3.BodyLarge">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>

    <style name="TextAppearance.Foodie.SearchHint" parent="TextAppearance.Material3.BodyLarge">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/text_secondary</item>
        <item name="android:fontFamily">sans-serif</item>
    </style>

    <style name="TextAppearance.Foodie.Rating" parent="TextAppearance.Material3.BodyMedium">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/text_secondary</item>
        <item name="android:fontFamily">sans-serif</item>
    </style>

    <style name="TextAppearance.Foodie.BottomNav" parent="TextAppearance.Material3.LabelSmall">
        <item name="android:textSize">12sp</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>

    <style name="TextAppearance.Foodie.NewBadge" parent="TextAppearance.Material3.LabelSmall">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/text_secondary</item>
        <item name="android:fontFamily">sans-serif</item>
    </style>

    <style name="TextAppearance.Foodie.FavoriteDescription" parent="TextAppearance.Material3.BodyMedium">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/text_secondary_favorites</item>
        <item name="android:fontFamily">sans-serif</item>
    </style>

    <style name="TextAppearance.Foodie.FavoriteDetails" parent="TextAppearance.Material3.BodyMedium">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/text_secondary_favorites</item>
        <item name="android:fontFamily">sans-serif</item>
    </style>

    <style name="TextAppearance.Foodie.SearchText" parent="TextAppearance.Material3.BodyLarge">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">sans-serif</item>
    </style>

    <style name="TextAppearance.Foodie.FilterLabel" parent="TextAppearance.Material3.LabelMedium">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:textAlignment">center</item>
    </style>

    <style name="TextAppearance.Foodie.SearchReviews" parent="TextAppearance.Material3.BodyMedium">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/text_secondary</item>
        <item name="android:fontFamily">sans-serif</item>
    </style>

    <style name="TextAppearance.Foodie.ProfileName" parent="TextAppearance.Material3.HeadlineSmall">
        <item name="android:textSize">22sp</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textAlignment">center</item>
    </style>

    <style name="TextAppearance.Foodie.ProfileEmail" parent="TextAppearance.Material3.BodyLarge">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/text_secondary_favorites</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:textAlignment">center</item>
    </style>

    <style name="TextAppearance.Foodie.ProfileSectionTitle" parent="TextAppearance.Material3.HeadlineSmall">
        <item name="android:textSize">18sp</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="TextAppearance.Foodie.ProfileItemTitle" parent="TextAppearance.Material3.BodyLarge">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>

    <style name="TextAppearance.Foodie.ProfileItemDetail" parent="TextAppearance.Material3.BodyMedium">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/text_secondary_favorites</item>
        <item name="android:fontFamily">sans-serif</item>
    </style>

    <style name="TextAppearance.Foodie.RestaurantDetailName" parent="TextAppearance.Material3.HeadlineSmall">
        <item name="android:textSize">22sp</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="TextAppearance.Foodie.LargeRating" parent="TextAppearance.Material3.DisplaySmall">
        <item name="android:textSize">36sp</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">sans-serif-black</item>
        <item name="android:letterSpacing">-0.0278</item>
    </style>

    <style name="TextAppearance.Foodie.RestaurantDetails" parent="TextAppearance.Material3.BodyLarge">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">sans-serif</item>
    </style>

    <style name="TextAppearance.Foodie.RestaurantDescription" parent="TextAppearance.Material3.BodyLarge">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:lineSpacingExtra">4dp</item>
    </style>

    <style name="TextAppearance.Foodie.RatingNumber" parent="TextAppearance.Material3.BodyMedium">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">sans-serif</item>
    </style>

    <style name="TextAppearance.Foodie.RatingPercentage" parent="TextAppearance.Material3.BodyMedium">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/text_secondary</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:textAlignment">textEnd</item>
    </style>

    <style name="TextAppearance.Foodie.ActionButton" parent="TextAppearance.Material3.LabelMedium">
        <item name="android:textSize">14sp</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textAlignment">center</item>
    </style>

    <!-- Card Styles -->
    <style name="Widget.Foodie.Card" parent="Widget.Material3.CardView.Elevated">
        <item name="cardCornerRadius">8dp</item>
        <item name="cardElevation">0dp</item>
        <item name="cardBackgroundColor">@color/background_primary</item>
    </style>

    <style name="Widget.Foodie.SearchCard" parent="Widget.Material3.CardView.Filled">
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">0dp</item>
        <item name="cardBackgroundColor">@color/background_secondary</item>
    </style>

    <style name="Widget.Foodie.DiscoverCard" parent="Widget.Material3.CardView.Elevated">
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">2dp</item>
        <item name="cardBackgroundColor">@color/background_primary</item>
    </style>

    <!-- Shape Appearance Styles -->
    <style name="ShapeAppearanceOverlay.Material3.Corner.Medium" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">8dp</item>
    </style>

    <style name="ShapeAppearanceOverlay.Material3.Corner.Full" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">50%</item>
    </style>
</resources>